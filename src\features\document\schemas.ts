import { z } from 'zod';

const mapDocumentTypeToSpanish = z.string().transform((type) => {
  const typeMap: Record<string, string> = {
    CEDULA: 'Cédula',
    RUT: 'RUT',
    ESTADOS_FINANCIEROS: 'Estados Financieros',
    CERTIFICADO_INGRESOS: 'Certificado de Ingresos',
    ESCRITURA_PUBLICA: 'Escritura Pública',
    AUTORIZACION: 'Autorización',
    DEMANDA: 'Demanda',
    RESPUESTA_DEMANDA: 'Respuesta a Demanda',
    ACUERDO: 'Acuerdo',
    SENTENCIA: 'Sentencia',
    OTRO: 'Otro',
  };
  return typeMap[type] || type;
});

const mapDocumentStatusToSpanish = z.string().transform((status) => {
  const statusMap: Record<string, string> = {
    PENDIENTE: 'Pendiente',
    APROBADO: 'Aprobado',
    RECHAZADO: 'Rechazado',
    EN_REVISION: 'En Revisión',
  };
  return statusMap[status] || status;
});

const mapCaseTypeToSpanish = z.string().transform((type) => {
  const typeMap: Record<string, string> = {
    INSOLVENCY: 'Insolvencia',
    CONCILIATION: 'Conciliación',
    SUPPORT_AGREEMENT: 'Acuerdo de Apoyo',
  };
  return typeMap[type] || type;
});

const mapCaseStatusToSpanish = z.string().transform((status) => {
  const statusMap: Record<string, string> = {
    NEGOTIATION: 'En negociación',
    HEARING_SCHEDULED: 'Audiencia programada',
    PENDING_DOCUMENTS: 'Documentos pendientes',
    AGREEMENT_APPROVED: 'Acuerdo aprobado',
    CLOSED: 'Cerrado',
  };
  return statusMap[status] || status;
});

const caseForDocumentSchema = z.object({
  id: z.string(),
  caseNumber: z.string(),
  debtorName: z.string(),
  type: mapCaseTypeToSpanish,
  status: mapCaseStatusToSpanish,
});

export const documentWithCaseSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: mapDocumentTypeToSpanish,
  status: mapDocumentStatusToSpanish,
  url: z.string(),
  uploadDate: z.coerce.date(),
  caseId: z.string(),
  case: caseForDocumentSchema,
});

export type DocumentWithCase = z.infer<typeof documentWithCaseSchema>;

export const documentSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  status: z.string(),
  url: z.string().optional(),
  uploadDate: z.coerce.date().optional(),
  caseId: z.string().optional(),
  case: caseForDocumentSchema.optional(),
  debtorName: z.string().optional(),
  createdDate: z.string().optional(),
  size: z.string().optional(),
  format: z.string().optional(),
  createdBy: z.string().optional(),
  downloadCount: z.coerce.number().optional(),
  lastAccessed: z.string().optional(),
  viewCount: z.coerce.number().optional(),
  shareCount: z.coerce.number().optional(),
});

export type Document = z.infer<typeof documentSchema>;

export const documentFilterSchema = z.object({
  caseId: z.string().optional(),
  type: z.string().optional(),
  status: z.string().optional(),
  search: z.string().optional(),
});

export type DocumentFilter = z.infer<typeof documentFilterSchema>;

export const documentStatsSchema = z.object({
  total: z.coerce.number(),
  byStatus: z.array(
    z.object({
      status: z.string(),
      _count: z.object({
        id: z.coerce.number(),
      }),
    }),
  ),
  byType: z.array(
    z.object({
      type: z.string(),
      _count: z.object({
        id: z.coerce.number(),
      }),
    }),
  ),
});

export type DocumentStats = z.infer<typeof documentStatsSchema>;

export const createDocumentSchema = z.object({
  name: z.string().min(1, 'El nombre es requerido'),
  type: z.string().min(1, 'El tipo es requerido'),
  status: z.string().optional().default('PENDIENTE'),
  url: z.string().url('URL inválida'),
  caseId: z.string().min(1, 'El ID del caso es requerido'),
});

export type CreateDocumentData = z.infer<typeof createDocumentSchema>;

export const updateDocumentSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
  name: z.string().min(1, 'El nombre es requerido').optional(),
  type: z.string().min(1, 'El tipo es requerido').optional(),
  status: z.string().optional(),
  url: z.string().url('URL inválida').optional(),
  caseId: z.string().min(1, 'El ID del caso es requerido').optional(),
});

export type UpdateDocumentData = z.infer<typeof updateDocumentSchema>;

export const deleteDocumentSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
});

export type DeleteDocumentData = z.infer<typeof deleteDocumentSchema>;

// Esquemas para plantillas de documentos
export const documentTemplateSchema = z.object({
  id: z.string(),
  googleDriveId: z.string(),
  fileName: z.string(),
  mimeType: z.string(),
  placeholders: z.array(
    z.object({
      key: z.string(),
      label: z.string(),
    }),
  ),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  // Folder hierarchy support
  folderPath: z.array(z.string()).default([]),
  parentFolderId: z.string().optional(),
  isFolder: z.boolean().default(false),
});

export type DocumentTemplate = z.infer<typeof documentTemplateSchema>;

export const editDocumentTemplateSchema = z.object({
  placeholders: z
    .array(
      z.object({
        key: z.string().optional(),
        label: z.string().optional(),
      }),
    )
    .optional()
    .default([]),
});

export type EditDocumentTemplateData = z.infer<
  typeof editDocumentTemplateSchema
>;

// Esquema para actualización de contenido de documentos
export const updateDocumentContentSchema = z.object({
  documentId: z.string().min(1, 'El ID del documento es requerido'),
  content: z.string().min(1, 'El contenido es requerido'),
  changes: z.string().optional(),
});

export type UpdateDocumentContentData = z.infer<
  typeof updateDocumentContentSchema
>;

// Batch document generation schemas
export const batchGenerateDocumentsSchema = z.object({
  caseId: z.string().min(1, 'El ID del caso es requerido'),
  templateIds: z.array(z.string()).optional(), // If empty, generate all templates
  outputFormat: z.enum(['docx', 'pdf']).default('docx'),
});

export type BatchGenerateDocumentsData = z.infer<
  typeof batchGenerateDocumentsSchema
>;

export const batchGenerationResultSchema = z.object({
  caseId: z.string(),
  caseNumber: z.string(),
  folderId: z.string(),
  folderUrl: z.string(),
  documents: z.array(
    z.object({
      templateId: z.string(),
      templateName: z.string(),
      documentId: z.string(),
      documentName: z.string(),
      documentUrl: z.string(),
      status: z.enum(['success', 'error']),
      error: z.string().optional(),
    }),
  ),
  generatedAt: z.coerce.date(),
  totalDocuments: z.number(),
  successfulDocuments: z.number(),
  failedDocuments: z.number(),
});

export type BatchGenerationResult = z.infer<typeof batchGenerationResultSchema>;

// Enhanced placeholder mapping schema
export const placeholderMappingSchema = z.object({
  case: z.object({
    caseNumber: z.string(),
    type: z.string(),
    status: z.string(),
    createdDate: z.string(),
    hearingDate: z.string().optional(),
    filingDate: z.string().optional(),
    totalDebt: z.string(),
    monthlyIncome: z.string(),
    monthlyExpenses: z.string(),
    debtor: z.object({
      name: z.string(),
      idNumber: z.string(),
      idType: z.string(),
      email: z.string(),
      phone: z.string(),
      address: z.string(),
      city: z.string().optional(),
      department: z.string().optional(),
      birthDate: z.string().optional(),
      maritalStatus: z.string().optional(),
      occupation: z.string().optional(),
      monthlyIncome: z.string(),
      monthlyExpenses: z.string(),
      dependents: z.string().optional(),
      educationLevel: z.string().optional(),
      emergencyContact: z.string().optional(),
      emergencyPhone: z.string().optional(),
      bankAccount: z.string().optional(),
      bankName: z.string().optional(),
      accountType: z.string().optional(),
    }),
    creditors: z.array(
      z.object({
        name: z.string(),
        amount: z.string(),
        interestRate: z.string(),
        type: z.string(),
      }),
    ),
    operator: z.object({
      name: z.string(),
      email: z.string(),
    }),
    assets: z
      .array(
        z.object({
          name: z.string(),
          type: z.string(),
          value: z.string(),
        }),
      )
      .optional(),
  }),
});

export type PlaceholderMapping = z.infer<typeof placeholderMappingSchema>;

// Document generation status schema
export const documentGenerationStatusSchema = z.object({
  id: z.string(),
  caseId: z.string(),
  status: z.enum(['pending', 'processing', 'completed', 'failed']),
  progress: z.number().min(0).max(100),
  totalDocuments: z.number(),
  processedDocuments: z.number(),
  successfulDocuments: z.number(),
  failedDocuments: z.number(),
  folderId: z.string().optional(),
  folderUrl: z.string().optional(),
  startedAt: z.coerce.date(),
  completedAt: z.coerce.date().optional(),
  error: z.string().optional(),
});

export type DocumentGenerationStatus = z.infer<
  typeof documentGenerationStatusSchema
>;

// Case with relations schema for batch document generation
export const debtorSchema = z
  .object({
    id: z.string(),
    name: z.string(),
    documentType: z.string().optional(),
    documentNumber: z.string().optional(),
    email: z.string().optional().nullable(),
    phone: z.string().optional().nullable(),
    address: z.string().optional().nullable(),
    city: z.string().optional().nullable(),
    department: z.string().optional().nullable(),
    country: z.string().optional().nullable(),
    birthDate: z.coerce.date().optional().nullable(),
    maritalStatus: z.string().optional().nullable(),
    occupation: z.string().optional().nullable(),
    monthlyIncome: z.number().optional().nullable(),
    createdAt: z.coerce.date().optional(),
    updatedAt: z.coerce.date().optional(),
    createdDate: z.coerce.date().optional(),
    lastUpdate: z.coerce.date().optional(),
  })
  .catchall(z.unknown());

export const operatorSchema = z
  .object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
    role: z.string().optional(),
  })
  .catchall(z.unknown());

export const creditorSchema = z
  .object({
    id: z.string(),
    name: z.string(),
    documentType: z.string().optional(),
    documentNumber: z.string().optional(),
    email: z.string().optional().nullable(),
    phone: z.string().optional().nullable(),
    address: z.string().optional().nullable(),
  })
  .catchall(z.unknown());

export const debtSchema = z
  .object({
    id: z.string(),
    amount: z.number().transform((val) => Number(val)), // Handle Decimal conversion
    currency: z.string(),
    description: z.string().optional().nullable(),
    dueDate: z.coerce.date().optional().nullable(),
    status: z.string(),
    creditor: creditorSchema.optional().nullable(),
  })
  .catchall(z.unknown());

export const assetSchema = z
  .object({
    id: z.string(),
    type: z.string(),
    description: z.string().optional().nullable(),
    estimatedValue: z.number().optional().nullable(),
    currency: z.string().optional().nullable(),
    location: z.string().optional().nullable(),
    status: z.string(),
  })
  .catchall(z.unknown());

export const caseWithRelationsSchema = z
  .object({
    id: z.string(),
    caseNumber: z.string(),
    type: z.string().optional(),
    status: z.string().optional(),
    createdAt: z.coerce.date().optional(),
    updatedAt: z.coerce.date().optional(),
    createdDate: z.coerce.date().optional(),
    lastUpdate: z.coerce.date().optional(),
    debtor: debtorSchema.optional().nullable(),
    operator: operatorSchema.optional().nullable(),
    debts: z.array(debtSchema).optional(),
    assets: z.array(assetSchema).optional(),
  })
  .catchall(z.unknown());

export type CaseWithRelations = z.infer<typeof caseWithRelationsSchema>;
export type Debtor = z.infer<typeof debtorSchema>;
export type Operator = z.infer<typeof operatorSchema>;
export type Debt = z.infer<typeof debtSchema>;
export type Asset = z.infer<typeof assetSchema>;
export type Creditor = z.infer<typeof creditorSchema>;

// Placeholder data schema
export const placeholderDataSchema = z.object({
  case: z
    .object({
      id: z.string(),
      caseNumber: z.string(),
      type: z.string().optional(),
      status: z.string().optional(),
      createdAt: z.coerce.date().optional(),
      updatedAt: z.coerce.date().optional(),
    })
    .catchall(z.unknown()),
  debtor: z
    .object({
      id: z.string().optional(),
      name: z.string().optional(),
      documentType: z.string().optional(),
      documentNumber: z.string().optional(),
      email: z.string().optional().nullable(),
      phone: z.string().optional().nullable(),
      address: z.string().optional().nullable(),
      city: z.string().optional().nullable(),
      department: z.string().optional().nullable(),
      country: z.string().optional().nullable(),
      birthDate: z.coerce.date().optional().nullable(),
      maritalStatus: z.string().optional().nullable(),
      occupation: z.string().optional().nullable(),
      monthlyIncome: z.number().optional().nullable(),
      createdAt: z.coerce.date().optional(),
      updatedAt: z.coerce.date().optional(),
    })
    .optional()
    .nullable(),
  operator: z
    .object({
      id: z.string().optional(),
      name: z.string().optional(),
      email: z.string().optional(),
      role: z.string().optional(),
    })
    .optional()
    .nullable(),
  debts: z
    .array(
      z.object({
        id: z.string(),
        amount: z.number(),
        currency: z.string(),
        description: z.string().optional().nullable(),
        dueDate: z.coerce.date().optional().nullable(),
        status: z.string(),
        creditor: z
          .object({
            id: z.string(),
            name: z.string(),
            documentType: z.string().optional(),
            documentNumber: z.string().optional(),
            email: z.string().optional().nullable(),
            phone: z.string().optional().nullable(),
            address: z.string().optional().nullable(),
          })
          .optional()
          .nullable(),
      }),
    )
    .optional(),
  assets: z
    .array(
      z.object({
        id: z.string(),
        type: z.string(),
        description: z.string().optional().nullable(),
        estimatedValue: z.number().optional().nullable(),
        currency: z.string().optional().nullable(),
        location: z.string().optional().nullable(),
        status: z.string(),
      }),
    )
    .optional(),
  currentDate: z.string(),
  currentDateTime: z.string(),
  totalDebts: z.number(),
  totalAssets: z.number(),
  debtCount: z.number(),
  assetCount: z.number(),
});

export type PlaceholderData = z.infer<typeof placeholderDataSchema>;

export const getDocumentsSchema = z.array(documentWithCaseSchema);
export const createDocumentOutputSchema = documentWithCaseSchema;
export const updateDocumentOutputSchema = documentWithCaseSchema;
export const deleteDocumentOutputSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  status: z.string(),
  url: z.string(),
  uploadDate: z.coerce.date(),
  caseId: z.string(),
});
