'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Folder,
  Loader2,
  Wand2,
  <PERSON>,
  Co<PERSON>,
  Info,
  FileDown,
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { useServerAction } from 'zsa-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { type Document } from '@/features/document/schemas';
import { DocumentTemplate } from '@/features/document/types';
import { getAllCases } from '@/features/case/actions';
import { toast } from 'sonner';
import {
  getDocuments,
  getTemplatesFromDatabase,
  syncTemplatesWithGoogleDrive,
  batchGenerateDocuments,
} from '@/features/document/actions';

import { DocumentDetailsDialog } from './document-details-dialog';
import { DocumentFileTree } from './document-file-tree';
import { DocumentTemplateLibrary } from './document-template-library';
import { DocumentViewerDialog } from './document-viewer-dialog';

const AVAILABLE_PLACEHOLDERS = [
  {
    key: 'case.caseNumber',
    label: 'Número de Caso',
    placeholder: '{{numeroCaso}}',
  },
  {
    key: 'case.debtorName',
    label: 'Nombre del Deudor',
    placeholder: '{{nombreDeudor}}',
  },
  { key: 'case.type', label: 'Tipo de Caso', placeholder: '{{tipoCaso}}' },
  {
    key: 'case.status',
    label: 'Estado del Caso',
    placeholder: '{{estadoCaso}}',
  },
  {
    key: 'case.createdDate',
    label: 'Fecha de Creación',
    placeholder: '{{fechaCreacion}}',
  },
  {
    key: 'case.hearingDate',
    label: 'Fecha de Audiencia',
    placeholder: '{{fechaAudiencia}}',
  },
  { key: 'case.phase', label: 'Fase del Caso', placeholder: '{{faseCaso}}' },
  { key: 'case.tramite', label: 'Trámite', placeholder: '{{tramite}}' },
  {
    key: 'case.filingDate',
    label: 'Fecha de Radicación',
    placeholder: '{{fechaRadicacion}}',
  },
  { key: 'case.attorney', label: 'Abogado', placeholder: '{{abogado}}' },
  {
    key: 'case.owedCapital',
    label: 'Capital Adeudado',
    placeholder: '{{capitalAdeudado}}',
  },
  {
    key: 'debtor.name',
    label: 'Nombre Completo del Deudor',
    placeholder: '{{deudorNombre}}',
  },
  {
    key: 'debtor.email',
    label: 'Email del Deudor',
    placeholder: '{{deudorEmail}}',
  },
  {
    key: 'debtor.phone',
    label: 'Teléfono del Deudor',
    placeholder: '{{deudorTelefono}}',
  },
  {
    key: 'debtor.address',
    label: 'Dirección del Deudor',
    placeholder: '{{deudorDireccion}}',
  },
  {
    key: 'debtor.city',
    label: 'Ciudad del Deudor',
    placeholder: '{{deudorCiudad}}',
  },
  {
    key: 'debtor.department',
    label: 'Departamento del Deudor',
    placeholder: '{{deudorDepartamento}}',
  },
  {
    key: 'debtor.monthlyIncome',
    label: 'Ingresos Mensuales del Deudor',
    placeholder: '{{deudorIngresosMensuales}}',
  },
  {
    key: 'debtor.monthlyExpenses',
    label: 'Gastos Mensuales del Deudor',
    placeholder: '{{deudorGastosMensuales}}',
  },
  {
    key: 'operator.name',
    label: 'Nombre del Operador',
    placeholder: '{{operadorNombre}}',
  },
  {
    key: 'operator.email',
    label: 'Email del Operador',
    placeholder: '{{operadorEmail}}',
  },
];

export function DocumentManagement() {
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(
    null,
  );
  const [isSyncing] = useState(false);

  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const [showViewerDialog, setShowViewerDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);

  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [showPlaceholdersDialog, setShowPlaceholdersDialog] = useState(false);
  const [selectedCaseForGeneration, setSelectedCaseForGeneration] =
    useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationResult, setGenerationResult] = useState<{
    caseId: string;
    caseNumber: string;
    folderId: string;
    folderUrl: string;
    documents: Array<{
      templateId: string;
      templateName: string;
      documentId: string;
      documentName: string;
      documentUrl: string;
      status: 'success' | 'error';
      error?: string;
    }>;
    generatedAt: Date;
    totalDocuments: number;
    successfulDocuments: number;
    failedDocuments: number;
  } | null>(null);
  const [cases, setCases] = useState<
    Array<{
      id: string;
      caseNumber: string;
      debtorName: string;
      type: string;
      status: string;
    }>
  >([]);

  const { execute: loadCases } = useServerAction(getAllCases, {
    onSuccess: ({ data }) => {
      setCases(
        data.map((c) => ({
          id: c.id,
          caseNumber: c.caseNumber,
          debtorName: c.debtorName,
          type: c.type,
          status: c.status,
        })),
      );
    },
  });

  const { execute: loadDocuments } = useServerAction(getDocuments, {
    onSuccess: ({ data }) => {
      setDocuments(data);
    },
  });

  const { execute: loadTemplates } = useServerAction(getTemplatesFromDatabase, {
    onSuccess: ({ data }) => {
      setTemplates(data);
    },
  });

  const { execute: handleSyncTemplates } = useServerAction(
    syncTemplatesWithGoogleDrive,
    {
      onSuccess: ({ data }) => {
        console.log(
          `Sincronización completada: ${data.synced} plantillas, ${data.created} creadas, ${data.updated} actualizadas, ${data.folders} carpetas`,
        );
        toast.success(
          `Sincronización completada: ${data.synced} plantillas sincronizadas`,
        );
        loadTemplates({});
        setRefreshTrigger((prev) => prev + 1);
      },
      onError: ({ err }) => {
        console.error('Error during sync:', err);
        toast.error(
          'Error al sincronizar con Google Drive: ' +
            (err.message || 'Error desconocido'),
        );
      },
    },
  );

  // Batch generation server action
  const { execute: executeBatchGeneration } = useServerAction(
    batchGenerateDocuments,
    {
      onSuccess: ({ data }) => {
        setIsGenerating(false);
        if (data?.data) {
          setGenerationResult(data.data);
          loadDocuments({}); // Refresh documents after generation
          toast.success('Documentos generados exitosamente');
        }
      },
      onError: ({ err }) => {
        setIsGenerating(false);
        console.error('Error in batch generation:', err);
        toast.error(
          'Error al generar documentos: ' +
            (err.message || 'Error desconocido'),
        );
      },
    },
  );

  const handleBatchGeneration = () => {
    if (!selectedCaseForGeneration) return;

    setIsGenerating(true);
    setGenerationResult(null);

    executeBatchGeneration({
      caseId: selectedCaseForGeneration,
      outputFormat: 'docx',
    });
  };

  useEffect(() => {
    loadCases();
    loadTemplates({});
    loadDocuments({});
  }, [loadCases, loadTemplates, loadDocuments]);

  const copyPlaceholderToClipboard = (placeholder: string) => {
    navigator.clipboard.writeText(placeholder);
    toast.success(`Placeholder ${placeholder} copiado al portapapeles`);
  };

  const [documents, setDocuments] = useState<Document[]>([]);

  const openDocumentViewer = (document: Document) => {
    setSelectedDocument(document);
    setShowViewerDialog(true);
  };

  const downloadDocument = (document: Document) => {
    setDocuments((docs) =>
      docs.map((doc) =>
        doc.id === document.id
          ? {
              ...doc,
              downloadCount: (doc.downloadCount ?? 0) + 1,
              lastAccessed: new Date().toISOString(),
            }
          : doc,
      ),
    );
    alert(`📥 Descargando: ${document.name}`);
  };

  const deleteDocument = (documentId: string) => {
    if (confirm('¿Está seguro de que desea eliminar este documento?')) {
      setDocuments(documents.filter((doc) => doc.id !== documentId));
      alert('✅ Documento eliminado exitosamente');
    }
  };

  const shareDocument = (document: Document) => {
    alert(`🔗 Compartiendo: ${document.name}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Gestión de Documentos
          </h1>
          <p className="text-gray-600">
            Administre documentos legales y sincronice plantillas desde Google
            Drive
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => handleSyncTemplates({})}>
            {(() => {
              if (isSyncing) {
                return (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sincronizando...
                  </>
                );
              }
              return (
                <>
                  <Cloud className="mr-2 h-4 w-4" />
                  Sincronizar con Drive
                </>
              );
            })()}
          </Button>
          <Button onClick={() => setShowPlaceholdersDialog(true)}>
            <Info className="mr-2 h-4 w-4" />
            Ver Placeholders
          </Button>
        </div>
      </div>

      {/* Inline Batch Generation Section */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5" />
            Generación de Documentos
          </CardTitle>
          <CardDescription>
            Genera todos los documentos de plantilla para un caso específico
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 lg:grid-cols-[1fr_auto] lg:items-end">
            <div className="space-y-2">
              <Label htmlFor="case-select">Seleccionar Caso</Label>
              <Select
                value={selectedCaseForGeneration}
                onValueChange={setSelectedCaseForGeneration}
              >
                <SelectTrigger id="case-select">
                  <SelectValue placeholder="Selecciona un caso..." />
                </SelectTrigger>
                <SelectContent>
                  {cases.map((caseItem) => (
                    <SelectItem key={caseItem.id} value={caseItem.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {caseItem.caseNumber}
                        </span>
                        <span className="text-muted-foreground text-sm">
                          {caseItem.debtorName}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="">
              <Button
                onClick={handleBatchGeneration}
                disabled={!selectedCaseForGeneration || isGenerating}
                className="w-full min-w-[180px] lg:w-auto"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generando...
                  </>
                ) : (
                  <>
                    <FileText className="mr-2 h-4 w-4" />
                    Generar Documentos
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Generation Results */}
          {generationResult && (
            <div className="mt-6 space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">
                  Resultados de Generación
                </h3>
                <Badge
                  variant={
                    generationResult.failedDocuments === 0
                      ? 'default'
                      : 'secondary'
                  }
                >
                  {generationResult.successfulDocuments}/
                  {generationResult.totalDocuments} exitosos
                </Badge>
              </div>

              <div className="bg-muted/50 space-y-2 rounded-lg p-4">
                <div className="flex items-center gap-2">
                  <Folder className="h-4 w-4" />
                  <span className="font-medium">
                    Caso: {generationResult.caseNumber}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <FileDown className="h-4 w-4" />
                  <a
                    href={generationResult.folderUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline"
                  >
                    Ver carpeta en Google Drive
                  </a>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Documentos Generados</h4>
                <div className="max-h-60 space-y-2 overflow-y-auto">
                  {generationResult.documents.map((doc, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between rounded-lg border p-3"
                    >
                      <div className="flex items-center gap-3">
                        {doc.status === 'success' ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <Copy className="h-4 w-4 text-red-600" />
                        )}
                        <div>
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            <span className="font-medium">
                              {doc.templateName}
                            </span>
                          </div>
                          {doc.error && (
                            <span className="text-sm text-red-600">
                              {doc.error}
                            </span>
                          )}
                        </div>
                      </div>
                      {doc.status === 'success' && doc.documentUrl && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(doc.documentUrl, '_blank')}
                        >
                          <FileDown className="mr-1 h-3 w-3" />
                          Abrir
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="documents" className="space-y-4">
        <TabsList>
          <TabsTrigger value="documents">Documentos</TabsTrigger>
          <TabsTrigger value="templates">Plantillas</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-4">
          <DocumentFileTree
            documents={documents}
            onViewDocument={openDocumentViewer}
            onDownloadDocument={downloadDocument}
          />
        </TabsContent>

        <TabsContent value="templates">
          <DocumentTemplateLibrary
            templates={templates}
            refreshTrigger={refreshTrigger}
          />
        </TabsContent>
      </Tabs>

      {/* Placeholders Dialog */}
      <Dialog
        open={showPlaceholdersDialog}
        onOpenChange={setShowPlaceholdersDialog}
      >
        <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[800px] md:max-w-[900px] lg:max-w-[1000px] xl:max-w-[1200px]">
          <DialogHeader>
            <DialogTitle>Placeholders Disponibles</DialogTitle>
            <DialogDescription>
              Estos son los placeholders que puedes usar en tus documentos. Haz
              clic en cualquiera para copiarlo al portapapeles.
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {AVAILABLE_PLACEHOLDERS.map((item) => (
              <Button
                key={item.key}
                variant="outline"
                className="h-auto justify-start p-3 text-left"
                onClick={() => copyPlaceholderToClipboard(item.placeholder)}
              >
                <div className="flex w-full items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">{item.label}</p>
                    <p className="font-mono text-xs text-gray-500">
                      {item.placeholder}
                    </p>
                  </div>
                  <Copy className="ml-2 h-4 w-4 text-gray-400" />
                </div>
              </Button>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {selectedDocument && (
        <>
          <DocumentViewerDialog
            open={showViewerDialog}
            onOpenChange={setShowViewerDialog}
            document={selectedDocument}
          />
          <DocumentDetailsDialog
            open={showDetailsDialog}
            onOpenChange={setShowDetailsDialog}
            document={selectedDocument}
            onDownload={downloadDocument}
            onShare={shareDocument}
            onDelete={deleteDocument}
          />
        </>
      )}
    </div>
  );
}
