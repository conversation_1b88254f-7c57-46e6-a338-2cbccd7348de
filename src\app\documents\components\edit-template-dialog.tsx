'use client';

import { useRef } from 'react';
import { Download, FileText, Eye, Info, Copy } from 'lucide-react';
import { toast } from 'sonner';
import { DocumentTemplate } from '@/features/document/types';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

const AVAILABLE_PLACEHOLDERS = [
  { label: 'Número de Caso', value: '{{case.caseNumber}}' },
  { label: 'Nombre del Deudor', value: '{{case.debtorName}}' },
  { label: 'Tipo de Caso', value: '{{case.type}}' },
  { label: 'Estado del Caso', value: '{{case.status}}' },
  { label: '<PERSON><PERSON> de Creación', value: '{{case.createdDate}}' },
  { label: 'Fecha de Audiencia', value: '{{case.hearingDate}}' },
  { label: 'Fase del Caso', value: '{{case.phase}}' },
  { label: 'Trámite', value: '{{case.tramite}}' },
  { label: 'Fecha de Radicación', value: '{{case.filingDate}}' },
  { label: 'Abogado', value: '{{case.attorney}}' },
  { label: 'Capital Adeudado', value: '{{case.owedCapital}}' },
  { label: 'Nombre Completo del Deudor', value: '{{debtor.name}}' },
  { label: 'Email del Deudor', value: '{{debtor.email}}' },
  { label: 'Teléfono del Deudor', value: '{{debtor.phone}}' },
  { label: 'Dirección del Deudor', value: '{{debtor.address}}' },
  { label: 'Ciudad del Deudor', value: '{{debtor.city}}' },
  { label: 'Departamento del Deudor', value: '{{debtor.department}}' },
  { label: 'Ingresos Mensuales del Deudor', value: '{{debtor.monthlyIncome}}' },
  { label: 'Gastos Mensuales del Deudor', value: '{{debtor.monthlyExpenses}}' },
  { label: 'Nombre del Operador', value: '{{operator.name}}' },
  { label: 'Email del Operador', value: '{{operator.email}}' },
] as const;

interface EditTemplateDialogProps {
  template: DocumentTemplate;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditTemplateDialog({
  template,
  open,
  onOpenChange,
}: Readonly<EditTemplateDialogProps>) {
  const closeRef = useRef<HTMLButtonElement>(null);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] w-[90vw] overflow-y-auto sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>Plantilla de Documento</DialogTitle>
          <DialogDescription>
            Visualiza y gestiona la plantilla: {template.fileName}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Sección de visualización del documento */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Documento de Plantilla
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between rounded-lg border p-4">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-blue-100 p-2">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium">{template.fileName}</p>
                    <p className="text-sm text-gray-500">
                      Archivo de plantilla en Google Drive
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const url = `/api/templates/${template.id}/preview`;
                      window.open(url, '_blank');
                    }}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    Vista Previa
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const url = `/api/templates/${template.id}/download`;
                      window.open(url, '_blank');
                    }}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Descargar
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Información sobre placeholders */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                Placeholders Disponibles
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Para usar datos dinámicos en tu documento, incluye estos
                  placeholders en el texto del documento de Word. Los
                  placeholders serán reemplazados automáticamente con los datos
                  del caso seleccionado al generar el documento.
                </AlertDescription>
              </Alert>

              <div className="mt-4 grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
                {AVAILABLE_PLACEHOLDERS.map((placeholder) => (
                  <div
                    key={placeholder.value}
                    className="flex items-center justify-between rounded-md border bg-gray-50 px-3 py-2"
                  >
                    <div className="flex min-w-0 flex-1 flex-col">
                      <span className="truncate text-sm font-medium">
                        {placeholder.label}
                      </span>
                      <span className="truncate font-mono text-xs text-gray-600">
                        {placeholder.value}
                      </span>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="ml-2 flex-shrink-0"
                      onClick={() => {
                        navigator.clipboard.writeText(placeholder.value);
                        toast.success('Placeholder copiado al portapapeles');
                      }}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <DialogClose asChild>
              <Button type="button" ref={closeRef}>
                Cerrar
              </Button>
            </DialogClose>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
