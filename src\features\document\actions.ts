'use server';

import { revalidatePath } from 'next/cache';
import { createServerAction } from 'zsa';
import { z } from 'zod';
import Docxtemplater from 'docxtemplater';
import <PERSON>z<PERSON><PERSON> from 'pizzip';
import libre from 'libreoffice-convert';
import { promisify } from 'util';

import prisma from '@/lib/prisma';

const libreConvert = promisify(libre.convert);

import {
  documentFilterSchema,
  documentStatsSchema,
  getDocumentsSchema,
  updateDocumentContentSchema,
  documentTemplateSchema,
  caseWithRelationsSchema,
  type CaseWithRelations,
} from './schemas';

export const getDocuments = createServerAction()
  .input(documentFilterSchema.optional())
  .output(getDocumentsSchema)
  .handler(async ({ input: filters }) => {
    return prisma.document.findMany({
      where: {
        ...(filters?.caseId && { caseId: filters.caseId }),
        ...(filters?.type && { type: filters.type }),
        ...(filters?.status && { status: filters.status }),
        ...(filters?.search && {
          OR: [
            { name: { contains: filters.search, mode: 'insensitive' } },
            {
              case: {
                caseNumber: { contains: filters.search, mode: 'insensitive' },
              },
            },
            {
              case: {
                debtorName: { contains: filters.search, mode: 'insensitive' },
              },
            },
          ],
        }),
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
      orderBy: {
        uploadDate: 'desc',
      },
    });
  });

// Acciones para plantillas de documentos
export const getDocumentTemplates = createServerAction()
  .input(z.object({}).optional())
  .output(z.array(documentTemplateSchema))
  .handler(async () => {
    const templates = await prisma.documentTemplate.findMany({
      orderBy: {
        fileName: 'asc',
      },
    });

    return templates.map((template) => ({
      ...template,
      parentFolderId: template.parentFolderId || undefined,
      placeholders: Array.isArray(template.placeholders)
        ? (
            template.placeholders as Array<{
              type: 'text' | 'number' | 'date' | 'boolean';
              required: boolean;
              key: string;
              label: string;
              description?: string;
              defaultValue?: string;
            }>
          ).map((p) => ({
            key: p.key,
            label: p.label,
          }))
        : [],
    }));
  });

export const downloadDocumentTemplate = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID de la plantilla es requerido') }),
  )
  .output(
    z.object({
      buffer: z.any(),
      fileName: z.string(),
      mimeType: z.string(),
    }),
  )
  .handler(async ({ input: { id } }) => {
    const template = await prisma.documentTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    const { googleDriveService } = await import('@/lib/google-drive');

    const fileInfo = await googleDriveService.getFileInfo(
      template.googleDriveId,
    );

    if (!fileInfo) {
      throw new Error('Plantilla no encontrada en Google Drive');
    }

    const buffer = await googleDriveService.downloadFile(
      template.googleDriveId,
    );

    return {
      buffer,
      fileName: fileInfo.name || template.fileName || 'template.docx',
      mimeType:
        fileInfo.mimeType || template.mimeType || 'application/octet-stream',
    };
  });

export const deleteDocumentTemplate = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID de la plantilla es requerido') }),
  )
  .output(z.object({ success: z.boolean() }))
  .handler(async ({ input: { id } }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    try {
      await googleDriveService.deleteFile(id);
    } catch (error) {
      console.error('Error eliminando archivo de Google Drive:', error);
      throw new Error('Error al eliminar la plantilla de Google Drive');
    }

    const existingTemplate = await prisma.documentTemplate.findFirst({
      where: { googleDriveId: id },
    });

    if (existingTemplate) {
      await prisma.documentTemplate.delete({
        where: { id: existingTemplate.id },
      });
    }

    revalidatePath('/documents/templates');
    return { success: true };
  });

export const getTemplatesFromDatabase = createServerAction()
  .input(z.object({}).optional())
  .output(z.array(documentTemplateSchema))
  .handler(async () => {
    const templates = await prisma.documentTemplate.findMany({
      orderBy: [{ isFolder: 'desc' }, { fileName: 'asc' }],
    });

    return templates.map((template) => ({
      id: template.id,
      googleDriveId: template.googleDriveId,
      fileName: template.fileName,
      mimeType: template.mimeType,
      placeholders: template.placeholders as Array<{
        key: string;
        label: string;
      }>,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
      folderPath: template.folderPath,
      isFolder: template.isFolder,
      parentFolderId: template.parentFolderId || undefined,
    }));
  });

export const getTemplatesFromGoogleDrive = createServerAction()
  .input(z.object({}).optional())
  .output(z.array(documentTemplateSchema))
  .handler(async () => {
    const { googleDriveService } = await import('@/lib/google-drive');

    const result = await googleDriveService.listFoldersAndFiles();

    const templateFiles = result.files.filter(
      (file) =>
        file.mimeType?.includes('word') ||
        file.mimeType?.includes('document') ||
        file.name?.toLowerCase().endsWith('.docx') ||
        file.name?.toLowerCase().endsWith('.doc'),
    );

    const templates = await Promise.all(
      templateFiles.map(async (file) => {
        let placeholders: Array<{
          type: 'text' | 'number' | 'date' | 'boolean';
          required: boolean;
          key: string;
          label: string;
          description?: string;
          defaultValue?: string;
        }> = [];

        let existingTemplate = await prisma.documentTemplate.findFirst({
          where: { googleDriveId: file.id! },
        });

        // If template doesn't exist in database, create it
        existingTemplate ??= await prisma.documentTemplate.create({
          data: {
            googleDriveId: file.id!,
            fileName: file.name!,
            mimeType: file.mimeType || 'application/octet-stream',
            placeholders: [],
            createdAt: file.createdTime
              ? new Date(file.createdTime)
              : new Date(),
            updatedAt: file.modifiedTime
              ? new Date(file.modifiedTime)
              : new Date(),
          },
        });

        if (existingTemplate && Array.isArray(existingTemplate.placeholders)) {
          placeholders = existingTemplate.placeholders as Array<{
            type: 'text' | 'number' | 'date' | 'boolean';
            required: boolean;
            key: string;
            label: string;
            description?: string;
            defaultValue?: string;
          }>;
        }

        return {
          id: existingTemplate.id,
          googleDriveId: file.id!,
          fileName: file.name!,
          mimeType: file.mimeType || 'application/octet-stream',
          placeholders: placeholders.map((p) => ({
            key: p.key,
            label: p.label,
          })),
          createdAt: file.createdTime ? new Date(file.createdTime) : new Date(),
          updatedAt: file.modifiedTime
            ? new Date(file.modifiedTime)
            : new Date(),
          folderPath: [],
          isFolder: false,
          parentFolderId: undefined,
        };
      }),
    );

    return templates;
  });

export const syncTemplatesWithGoogleDrive = createServerAction()
  .input(z.object({}).optional())
  .output(
    z.object({
      synced: z.number(),
      updated: z.number(),
      created: z.number(),
      folders: z.number(),
    }),
  )
  .handler(async () => {
    console.log('Starting Google Drive sync process...');
    const { googleDriveService } = await import('@/lib/google-drive');

    console.log('Fetching folders and files from Google Drive...');
    const driveData =
      await googleDriveService.listAllFoldersAndFilesRecursively();
    console.log(
      `Found ${driveData.folders.length} folders and ${driveData.files.length} files in Google Drive`,
    );

    let created = 0;
    let updated = 0;
    let folders = 0;

    // First, sync all folders
    for (const folder of driveData.folders) {
      const existingTemplate = await prisma.documentTemplate.findFirst({
        where: { googleDriveId: folder.id },
      });

      if (existingTemplate) {
        await prisma.documentTemplate.update({
          where: { id: existingTemplate.id },
          data: {
            fileName: folder.name,
            folderPath: folder.path,
            parentFolderId: folder.parentId,
            isFolder: true,
            updatedAt: folder.modifiedTime
              ? new Date(folder.modifiedTime)
              : new Date(),
          },
        });
        updated++;
      } else {
        await prisma.documentTemplate.create({
          data: {
            googleDriveId: folder.id,
            fileName: folder.name,
            mimeType: 'application/vnd.google-apps.folder',
            placeholders: [],
            folderPath: folder.path,
            parentFolderId: folder.parentId,
            isFolder: true,
            createdAt: folder.createdTime
              ? new Date(folder.createdTime)
              : new Date(),
            updatedAt: folder.modifiedTime
              ? new Date(folder.modifiedTime)
              : new Date(),
          },
        });
        created++;
      }
      folders++;
    }

    // Then, sync all files (filter for document types)
    const templateFiles = driveData.files.filter(
      (file) =>
        file.mimeType?.includes('word') ||
        file.mimeType?.includes('document') ||
        file.name?.toLowerCase().endsWith('.docx') ||
        file.name?.toLowerCase().endsWith('.doc') ||
        file.mimeType?.includes('spreadsheet') ||
        file.name?.toLowerCase().endsWith('.xlsx') ||
        file.name?.toLowerCase().endsWith('.xls'),
    );

    for (const file of templateFiles) {
      const existingTemplate = await prisma.documentTemplate.findFirst({
        where: { googleDriveId: file.id },
      });

      if (existingTemplate) {
        await prisma.documentTemplate.update({
          where: { id: existingTemplate.id },
          data: {
            fileName: file.name,
            mimeType: file.mimeType || 'application/octet-stream',
            folderPath: file.path,
            parentFolderId: file.parentId,
            isFolder: false,
            updatedAt: file.modifiedTime
              ? new Date(file.modifiedTime)
              : new Date(),
          },
        });
        updated++;
      } else {
        await prisma.documentTemplate.create({
          data: {
            googleDriveId: file.id,
            fileName: file.name,
            mimeType: file.mimeType || 'application/octet-stream',
            placeholders: [],
            folderPath: file.path,
            parentFolderId: file.parentId,
            isFolder: false,
            createdAt: file.createdTime
              ? new Date(file.createdTime)
              : new Date(),
            updatedAt: file.modifiedTime
              ? new Date(file.modifiedTime)
              : new Date(),
          },
        });
        created++;
      }
    }

    const syncResult = {
      synced: templateFiles.length + folders,
      created,
      updated,
      folders,
    };

    console.log('Sync completed:', syncResult);
    return syncResult;
  });

export const updateDocumentContent = createServerAction()
  .input(updateDocumentContentSchema)
  .output(z.object({ success: z.boolean() }))
  .handler(async ({ input: data }) => {
    // Obtener el documento actual
    const currentDocument = await prisma.document.findUnique({
      where: { id: data.documentId },
    });

    if (!currentDocument) {
      throw new Error('Documento no encontrado');
    }

    // For now, we'll just update the document name or status
    // In the future, this could upload a new version to Google Drive
    await prisma.document.update({
      where: { id: data.documentId },
      data: {
        name: data.content.substring(0, 100), // Use first 100 chars as name
        status: 'ACTUALIZADO',
      },
    });

    revalidatePath('/documents');
    return { success: true };
  });

export const extractPlaceholdersFromTemplate = createServerAction()
  .input(z.object({ templateId: z.string() }))
  .output(
    z.array(
      z.object({
        key: z.string(),
        label: z.string(),
        type: z.enum(['text', 'number', 'date', 'email']),
        required: z.boolean(),
        description: z.string().optional(),
      }),
    ),
  )
  .handler(async ({ input: { templateId } }) => {
    const template = await prisma.documentTemplate.findUnique({
      where: { id: templateId },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    try {
      // Obtener el archivo de Google Drive usando el servicio existente
      const { googleDriveService } = await import('@/lib/google-drive');
      const templateBuffer = await googleDriveService.downloadFile(
        template.googleDriveId,
      );

      // Extraer placeholders del documento Word
      const placeholders =
        await extractPlaceholdersFromWordDocument(templateBuffer);

      return placeholders;
    } catch (error) {
      console.error('Error extracting placeholders:', error);
      throw new Error('Error al extraer placeholders del documento');
    }
  });

async function extractPlaceholdersFromWordDocument(buffer: Buffer): Promise<
  Array<{
    key: string;
    label: string;
    type: 'text' | 'number' | 'date' | 'email';
    required: boolean;
    description?: string;
  }>
> {
  try {
    const JSZip = (await import('jszip')).default;
    const zip = await JSZip.loadAsync(buffer);

    // Leer el contenido principal del documento
    const documentXml = await zip.file('word/document.xml')?.async('text');
    if (!documentXml) {
      throw new Error('No se pudo leer el contenido del documento');
    }

    const placeholderPatterns = [
      /\{\{([^}]+)\}\}/g,
      /\[([^\]]+)\]/g,
      /%([^%]+)%/g,
    ];

    const foundPlaceholders = new Set<string>();

    for (const pattern of placeholderPatterns) {
      let match;
      while ((match = pattern.exec(documentXml)) !== null) {
        const placeholder = match[1].trim();
        if (placeholder && placeholder.length > 0) {
          foundPlaceholders.add(placeholder);
        }
      }
    }

    // Convertir a formato de placeholder con tipos inferidos
    return Array.from(foundPlaceholders).map((key) => {
      const lowerKey = key.toLowerCase();
      let type: 'text' | 'number' | 'date' | 'email' = 'text';

      if (lowerKey.includes('fecha') || lowerKey.includes('date')) {
        type = 'date';
      } else if (lowerKey.includes('email') || lowerKey.includes('correo')) {
        type = 'email';
      } else if (
        lowerKey.includes('numero') ||
        lowerKey.includes('cantidad') ||
        lowerKey.includes('monto')
      ) {
        type = 'number';
      }

      return {
        key: key.toLowerCase().replace(/\s+/g, '_'),
        label: key.charAt(0).toUpperCase() + key.slice(1),
        type,
        required: false,
      };
    });
  } catch (error) {
    console.error('Error parsing Word document:', error);
    return [];
  }
}

export const getDocumentStats = createServerAction()
  .output(documentStatsSchema)
  .handler(async () => {
    const [total, byStatus, byType] = await Promise.all([
      prisma.document.count(),
      prisma.document.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      }),
      prisma.document.groupBy({
        by: ['type'],
        _count: {
          id: true,
        },
      }),
    ]);

    return {
      total,
      byStatus,
      byType,
    };
  });

export const generateDocumentWithPlaceholders = createServerAction()
  .input(
    z.object({
      templateId: z.string(),
      caseId: z.string(),
      format: z.enum(['docx', 'pdf']),
    }),
  )
  .output(
    z.object({
      success: z.boolean(),
      message: z.string(),
      data: z
        .object({
          documentId: z.string(),
          fileName: z.string(),
          buffer: z.any(),
          mimeType: z.string(),
        })
        .nullable(),
    }),
  )
  .handler(async ({ input }) => {
    try {
      const { templateId, caseId, format } = input;

      // Get case data with all related information
      const caseData = await prisma.case.findUnique({
        where: { id: caseId },
        include: {
          debtor: true,
          operator: true,
        },
      });

      if (!caseData) {
        throw new Error('Caso no encontrado');
      }

      // Get template data
      const template = await prisma.documentTemplate.findUnique({
        where: { id: templateId },
      });

      if (!template || !template.googleDriveId) {
        throw new Error('Plantilla no encontrada');
      }

      // Download template from Google Drive
      const { googleDriveService } = await import('@/lib/google-drive');
      const templateBuffer = await googleDriveService.downloadFile(
        template.googleDriveId,
      );

      // Prepare data for template replacement
      const templateData = {
        numeroCaso: caseData.caseNumber,
        nombreDeudor: caseData.debtorName,
        tipoCaso: caseData.type,
        estadoCaso: caseData.status,
        fechaCreacion: caseData.createdDate.toLocaleDateString('es-ES'),
        fechaAudiencia: caseData.hearingDate?.toLocaleDateString('es-ES') || '',
        faseCaso: caseData.phase || '',
        tramite: caseData.tramite || '',
        fechaRadicacion: caseData.filingDate?.toLocaleDateString('es-ES') || '',
        abogado: caseData.attorney || '',
        capitalAdeudado: caseData.totalDebt?.toString() || '',
        deudorNombre: caseData.debtor?.name || '',
        deudorEmail: caseData.debtor?.email || '',
        deudorTelefono: caseData.debtor?.phone || '',
        deudorDireccion: caseData.debtor?.address || '',
        deudorCiudad: caseData.debtor?.city || '',
        deudorDepartamento: caseData.debtor?.department || '',
        deudorIngresosMensuales:
          caseData.debtor?.monthlyIncome?.toString() || '',
        deudorGastosMensuales:
          caseData.debtor?.monthlyExpenses?.toString() || '',
        operadorNombre: caseData.operator?.name || '',
        operadorEmail: caseData.operator?.email || '',
      };

      // Process template with docxtemplater
      const zip = new PizZip(templateBuffer);
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true,
      });

      doc.render(templateData);

      const docxBuffer = doc.getZip().generate({
        type: 'nodebuffer',
        compression: 'DEFLATE',
      });

      let finalBuffer = docxBuffer;
      let fileName = `${caseData.caseNumber}_${template.fileName}`;
      let mimeType =
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

      // Convert to PDF if requested
      if (format === 'pdf') {
        try {
          finalBuffer = await libreConvert(docxBuffer, '.pdf', undefined);
          fileName = fileName.replace('.docx', '.pdf');
          mimeType = 'application/pdf';
        } catch (error) {
          console.error('Error converting to PDF:', error);
          throw new Error('Error al convertir el documento a PDF');
        }
      }

      // Save generated document to database
      const generatedDocument = await prisma.document.create({
        data: {
          name: fileName,
          type: format.toUpperCase(),
          status: 'GENERADO',
          url: `#generated-${Date.now()}`, // Temporary URL for generated documents
          caseId: caseData.id,
          templateId: template.id,
          isGenerated: true,
        },
      });

      revalidatePath('/documents');

      return {
        success: true,
        message: 'Documento generado exitosamente',
        data: {
          documentId: generatedDocument.id,
          fileName,
          buffer: finalBuffer,
          mimeType,
        },
      };
    } catch (error) {
      console.error('Error generating document:', error);
      return {
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Error al generar el documento',
        data: null,
      };
    }
  });

// Batch document generation actions
export const batchGenerateDocuments = createServerAction()
  .input(
    z.object({
      caseId: z.string().min(1, 'El ID del caso es requerido'),
      templateIds: z.array(z.string()).optional(),
      outputFormat: z.enum(['docx', 'pdf']).default('docx'),
    }),
  )
  .output(
    z.object({
      success: z.boolean(),
      message: z.string(),
      data: z
        .object({
          caseId: z.string(),
          caseNumber: z.string(),
          folderId: z.string(),
          folderUrl: z.string(),
          documents: z.array(
            z.object({
              templateId: z.string(),
              templateName: z.string(),
              documentId: z.string(),
              documentName: z.string(),
              documentUrl: z.string(),
              status: z.enum(['success', 'error']),
              error: z.string().optional(),
            }),
          ),
          generatedAt: z.coerce.date(),
          totalDocuments: z.number(),
          successfulDocuments: z.number(),
          failedDocuments: z.number(),
        })
        .nullable(),
    }),
  )
  .handler(async ({ input: { caseId, templateIds, outputFormat } }) => {
    try {
      // Get case data with all relations
      const caseData = await getCaseWithRelationsForGeneration(caseId);
      if (!caseData) {
        throw new Error('Caso no encontrado');
      }

      // Get templates to process
      const templates = await getTemplatesToProcess(templateIds);
      if (templates.length === 0) {
        throw new Error('No se encontraron plantillas para procesar');
      }

      // Create case folder in Google Drive
      const caseFolder = await createCaseFolderInGoogleDrive(
        caseData.caseNumber,
      );

      // Process each template
      // Parse and validate case data with schema to handle Decimal conversion
      const validatedCaseData = caseWithRelationsSchema.parse({
        ...caseData,
        debts:
          caseData.debts?.map((debt) => ({
            ...debt,
            amount: Number(debt.amount), // Convert Decimal to number
            creditor: debt.creditor
              ? {
                  ...debt.creditor,
                }
              : null,
          })) || [],
        assets:
          caseData.assets?.map((asset) => ({
            ...asset,
            estimatedValue: asset.value ? Number(asset.value) : null,
          })) || [],
        debtor: caseData.debtor
          ? {
              ...caseData.debtor,
              monthlyIncome: caseData.debtor.monthlyIncome
                ? Number(caseData.debtor.monthlyIncome)
                : null,
            }
          : null,
      });

      const results = await Promise.allSettled(
        templates.map((template) =>
          processTemplateForCase(
            template,
            validatedCaseData,
            caseFolder,
            outputFormat,
          ),
        ),
      );

      // Compile results
      const documents = results.map((result, index) => {
        const template = templates[index];
        if (result.status === 'fulfilled') {
          return {
            templateId: template.id,
            templateName: template.fileName,
            documentId: result.value.documentId,
            documentName: result.value.documentName,
            documentUrl: result.value.documentUrl,
            status: 'success' as const,
          };
        } else {
          return {
            templateId: template.id,
            templateName: template.fileName,
            documentId: '',
            documentName: '',
            documentUrl: '',
            status: 'error' as const,
            error: result.reason?.message || 'Error desconocido',
          };
        }
      });

      const successfulDocuments = documents.filter(
        (doc) => doc.status === 'success',
      ).length;
      const failedDocuments = documents.length - successfulDocuments;

      revalidatePath('/documents');

      return {
        success: true,
        message: `Generación completada: ${successfulDocuments} exitosos, ${failedDocuments} fallidos`,
        data: {
          caseId: caseData.id,
          caseNumber: caseData.caseNumber,
          folderId: caseFolder.id,
          folderUrl: caseFolder.webViewLink,
          documents,
          generatedAt: new Date(),
          totalDocuments: documents.length,
          successfulDocuments,
          failedDocuments,
        },
      };
    } catch (error) {
      console.error('Error in batch document generation:', error);
      return {
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Error en la generación por lotes',
        data: null,
      };
    }
  });

// Helper function to get case data with all relations for document generation
async function getCaseWithRelationsForGeneration(caseId: string) {
  return await prisma.case.findUnique({
    where: { id: caseId },
    include: {
      debtor: true,
      operator: true,
      debts: {
        include: {
          creditor: true,
        },
      },
      assets: true,
      documents: true,
    },
  });
}

// Helper function to get templates to process
async function getTemplatesToProcess(templateIds?: string[]) {
  if (templateIds && templateIds.length > 0) {
    // Get specific templates
    return await prisma.documentTemplate.findMany({
      where: {
        id: { in: templateIds },
      },
    });
  } else {
    // Get all templates
    return await prisma.documentTemplate.findMany();
  }
}

// Helper function to create case folder in Google Drive
async function createCaseFolderInGoogleDrive(caseNumber: string) {
  const { googleDriveService } = await import('@/lib/google-drive');
  const casesFolderId = '1jV6Kf5Y7jDPDUn2vmzpSvFGosftsWS_2';

  const caseFolderId = await googleDriveService.findOrCreateCaseFolder(
    caseNumber,
    casesFolderId,
  );

  return {
    id: caseFolderId,
    name: caseNumber,
    webViewLink: googleDriveService.getFolderUrl(caseFolderId),
  };
}

// Helper function to process a single template for a case
async function processTemplateForCase(
  template: {
    id: string;
    googleDriveId: string;
    fileName: string;
  },
  caseData: CaseWithRelations,
  caseFolder: { id: string; name: string; webViewLink: string },
  outputFormat: 'docx' | 'pdf',
) {
  try {
    const { googleDriveService } = await import('@/lib/google-drive');

    // Download template from Google Drive
    const templateBuffer = await googleDriveService.downloadFile(
      template.googleDriveId,
    );

    // Generate document with placeholders filled
    const generatedBuffer = await generateDocumentWithPlaceholdersInternal(
      templateBuffer,
      caseData,
      outputFormat,
    );

    // Create document name
    const documentName = `${template.fileName.replace(/\.[^/.]+$/, '')}_${caseData.caseNumber}.${outputFormat}`;

    // Upload to case folder in Google Drive
    const { fileId, fileUrl } = await googleDriveService.uploadDocumentToCase(
      documentName,
      generatedBuffer,
      caseFolder.id,
    );

    // Save document record in database
    const document = await prisma.document.create({
      data: {
        name: documentName,
        type: getDocumentTypeFromTemplate(template.fileName),
        status: 'Generado',
        uploadDate: new Date(),
        url: fileUrl,
        googleDriveId: fileId,
        caseId: caseData.id,
        templateId: template.id,
      },
    });

    return {
      documentId: document.id,
      documentName,
      documentUrl: fileUrl,
    };
  } catch (error) {
    console.error(`Error processing template ${template.fileName}:`, error);
    throw error;
  }
}

// Helper function to generate document with placeholders filled
async function generateDocumentWithPlaceholdersInternal(
  templateBuffer: Buffer,
  caseData: CaseWithRelations,
  outputFormat: 'docx' | 'pdf',
): Promise<Buffer> {
  try {
    const zip = new PizZip(templateBuffer);
    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
    });

    // Create placeholder data from case information
    const placeholderData = createPlaceholderData(caseData);

    // Render the document with data
    doc.render(placeholderData);

    // Get the generated buffer
    const generatedBuffer = doc.getZip().generate({ type: 'nodebuffer' });

    // Convert to PDF if requested
    if (outputFormat === 'pdf') {
      return await libreConvert(generatedBuffer, '.pdf', undefined);
    }

    return generatedBuffer;
  } catch (error) {
    console.error('Error generating document with placeholders:', error);
    throw new Error('Error al generar documento con placeholders');
  }
}

// Helper function to create placeholder data from case data
function createPlaceholderData(caseData: CaseWithRelations) {
  return {
    // Case information
    case: {
      id: caseData.id,
      caseNumber: caseData.caseNumber,
      type: caseData.type,
      status: caseData.status,
      createdAt: caseData.createdAt,
      updatedAt: caseData.updatedAt,
    },

    // Debtor information
    debtor: caseData.debtor
      ? {
          id: caseData.debtor.id,
          name: caseData.debtor.name,
          documentType: caseData.debtor.documentType,
          documentNumber: caseData.debtor.documentNumber,
          email: caseData.debtor.email,
          phone: caseData.debtor.phone,
          address: caseData.debtor.address,
          city: caseData.debtor.city,
          department: caseData.debtor.department,
          country: caseData.debtor.country,
          birthDate: caseData.debtor.birthDate,
          maritalStatus: caseData.debtor.maritalStatus,
          occupation: caseData.debtor.occupation,
          monthlyIncome: caseData.debtor.monthlyIncome,
          createdAt: caseData.debtor.createdAt,
          updatedAt: caseData.debtor.updatedAt,
        }
      : null,

    // Operator information
    operator: caseData.operator
      ? {
          id: caseData.operator.id,
          name: caseData.operator.name,
          email: caseData.operator.email,
          role: caseData.operator.role,
        }
      : null,

    // Debts information
    debts: caseData.debts
      ? caseData.debts.map((debt) => ({
          id: debt.id,
          amount: debt.amount,
          currency: debt.currency,
          description: debt.description,
          dueDate: debt.dueDate,
          status: debt.status,
          creditor: debt.creditor
            ? {
                id: debt.creditor.id,
                name: debt.creditor.name,
                documentType: debt.creditor.documentType,
                documentNumber: debt.creditor.documentNumber,
                email: debt.creditor.email,
                phone: debt.creditor.phone,
                address: debt.creditor.address,
              }
            : null,
        }))
      : [],

    // Assets information
    assets: caseData.assets
      ? caseData.assets.map((asset) => ({
          id: asset.id,
          type: asset.type,
          description: asset.description || null,
          estimatedValue: asset.estimatedValue || null,
          currency: asset.currency || null,
          location: asset.location || null,
          status: asset.status,
        }))
      : [],

    // Current date and time
    currentDate: new Date().toLocaleDateString('es-CO'),
    currentDateTime: new Date().toLocaleString('es-CO'),

    // Totals and calculations
    totalDebts: caseData.debts
      ? caseData.debts.reduce(
          (sum: number, debt) => sum + (Number(debt.amount) || 0),
          0,
        )
      : 0,
    totalAssets: caseData.assets
      ? caseData.assets.reduce(
          (sum: number, asset) => sum + (Number(asset.estimatedValue) || 0),
          0,
        )
      : 0,
    debtCount: caseData.debts ? caseData.debts.length : 0,
    assetCount: caseData.assets ? caseData.assets.length : 0,
  };
}

// Helper function to determine document type from template filename
function getDocumentTypeFromTemplate(fileName: string): string {
  const lowerFileName = fileName.toLowerCase();

  if (lowerFileName.includes('solicitud')) return 'Solicitud';
  if (lowerFileName.includes('declaracion')) return 'Declaración';
  if (lowerFileName.includes('certificado')) return 'Certificado';
  if (lowerFileName.includes('check') || lowerFileName.includes('lista'))
    return 'Lista de Verificación';
  if (lowerFileName.includes('designacion')) return 'Designación';
  if (lowerFileName.includes('auto')) return 'Auto';
  if (lowerFileName.includes('inadmision')) return 'Inadmisión';
  if (lowerFileName.includes('notificacion')) return 'Notificación';
  if (lowerFileName.includes('edicto')) return 'Edicto';
  if (lowerFileName.includes('suspension')) return 'Suspensión';
  if (lowerFileName.includes('formato')) return 'Formato';
  if (lowerFileName.includes('remision')) return 'Remisión';
  if (lowerFileName.includes('guia')) return 'Guía';
  if (lowerFileName.includes('hoja')) return 'Hoja de Trabajo';

  return 'Documento';
}

// Get batch generation status
export const getBatchGenerationStatus = createServerAction()
  .input(z.object({ caseId: z.string() }))
  .output(
    z.object({
      success: z.boolean(),
      data: z
        .object({
          id: z.string(),
          caseId: z.string(),
          status: z.enum(['pending', 'processing', 'completed', 'failed']),
          progress: z.number(),
          totalDocuments: z.number(),
          processedDocuments: z.number(),
          successfulDocuments: z.number(),
          failedDocuments: z.number(),
          folderId: z.string().optional(),
          folderUrl: z.string().optional(),
          startedAt: z.coerce.date(),
          completedAt: z.coerce.date().optional(),
          error: z.string().optional(),
        })
        .nullable(),
    }),
  )
  .handler(async () => {
    // This would typically query a job status table
    // For now, we'll return a mock implementation
    return {
      success: true,
      data: null, // No active generation found
    };
  });
